url: https://www.youtube.com/watch?v=H5FAxTBuNM8&t=8648s
from 01:26:00

1. npm create vite@latest react-docker and cd react-docker  

2. no npm i

3. create a file named "Dockerfile" in the root directory of the project.

4. docker build -t react-docker .

5. docker run react-docker

6. port mapping -> docker run -p 5173:5173 react-docker

7. package.json -> "start": "vite --host"

8. docker ps -a 

9. docker stop 5eb9 

10. docker container prune

11. docker rm aa7 --force


12. remove all from docker , 
    - then docker build -t react-docker .
    - docker run -it -p 5173:5173 react-docker

13. docker run -it -p 5173:5173 -v "$(pwd):/app" -v /app/node_modules react-docker
        - it monitors any changes in current dev directory and changes the docker volumes of the container
        -v "$(pwd):/app" -> mount the current directory to /app
        -v /app/node_modules -> mount the node_modules directory to /app/node_modules
        -it -> interactive mode
        -p 5173:5173 -> port mapping
        react-docker -> image name

